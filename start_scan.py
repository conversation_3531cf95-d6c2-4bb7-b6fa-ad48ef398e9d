#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网易云音乐短链扫描器 - 快速启动脚本
"""

import os
import sys
import subprocess

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import requests
        print("✅ requests 库已安装")
        return True
    except ImportError:
        print("❌ 缺少 requests 库")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装 requests 库...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "requests"])
        print("✅ requests 库安装成功")
        return True
    except subprocess.CalledProcessError:
        print("❌ requests 库安装失败")
        return False

def check_config():
    """检查配置文件"""
    if os.path.exists("config.py"):
        print("✅ 找到配置文件 config.py")
        return True
    else:
        print("⚠️ 未找到配置文件 config.py，将使用默认配置")
        return False

def get_latest_link():
    """获取用户输入的最新短链"""
    print("\n" + "="*60)
    print("📋 配置向导")
    print("="*60)
    
    print("\n请从论坛或其他渠道获取一个最新的有效短链作为起始点")
    print("示例: http://163cn.tv/FBm5xqZ")
    print("只需要输入后6位: Bm5xqZ")
    
    while True:
        suffix = input("\n请输入最新短链的后6位: ").strip()
        if len(suffix) == 6:
            return suffix
        else:
            print("❌ 请输入正确的6位字符")

def create_quick_config(suffix):
    """创建快速配置文件"""
    config_content = f'''# 网易云音乐短链扫描器 - 快速配置

# 基础配置
PREFIX = 'F'
START_SUFFIX = "{suffix}"
SCAN_RANGE = 900000000

# 文件配置
VIP_FILE = 'vip_links.txt'
GIFT_FILE = 'gift_links.txt'
LOG_FILE = 'scan_log.txt'

# 扫描配置
REQUEST_TIMEOUT = 5
SLEEP_EVERY = 50
SLEEP_DURATION = 2
STATS_EVERY = 1000

# 神奇数字
MAGIC_4 = 4
MAGIC_344 = 344

# Base62字符表
BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

# 活动类型
ACTIVITY_TYPES = {{
    'vip-invite-cashier': '随机88天或188天网易云黑胶VIP',
    'gift-receive': '每月5*7天网易云黑胶VIP（会员等级低可能是5*3天）'
}}
'''
    
    with open("config.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print(f"✅ 已创建配置文件 config.py，起始后缀设置为: {suffix}")

def main():
    """主函数"""
    print("🚀 网易云音乐短链扫描器 - 快速启动")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        if input("是否自动安装 requests 库? (y/n): ").lower() == 'y':
            if not install_dependencies():
                print("❌ 依赖安装失败，请手动安装: pip install requests")
                return
        else:
            print("❌ 请手动安装依赖: pip install requests")
            return
    
    # 检查配置
    if not check_config():
        if input("是否创建快速配置? (y/n): ").lower() == 'y':
            suffix = get_latest_link()
            create_quick_config(suffix)
        else:
            print("⚠️ 将使用默认配置运行")
    
    # 启动扫描
    print("\n" + "="*60)
    print("🎯 准备启动扫描...")
    print("="*60)
    
    if input("是否开始扫描? (y/n): ").lower() == 'y':
        print("\n🚀 启动扫描器...")
        try:
            import short_link_checker
        except ImportError:
            print("❌ 未找到 short_link_checker.py 文件")
    else:
        print("👋 扫描已取消")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")

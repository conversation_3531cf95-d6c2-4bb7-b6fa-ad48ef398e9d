@echo off
echo 🚀 启动网易云音乐礼品卡分析器
echo ================================

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8+
    pause
    exit /b 1
)

:: 检查项目文件
if not exist "wyy-Link-Alive-main\gift_analyzer_ui.py" (
    echo ❌ 错误: 未找到项目文件
    echo 请确保在正确的目录下运行此脚本
    pause
    exit /b 1
)

:: 检查依赖
echo 📦 检查依赖包...
python -c "import PyQt6, requests, Crypto" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 依赖包不完整，正在安装...
    pip install -r wyy-Link-Alive-main\requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 环境检查完成
echo 🎵 启动网易云音乐礼品卡分析器...
echo.

:: 启动程序
cd /d "%~dp0"
python wyy-Link-Alive-main\gift_analyzer_ui.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 程序异常退出
    echo 可能的原因:
    echo   1. 缺少必要的依赖包
    echo   2. Python版本不兼容
    echo   3. 系统权限问题
    echo.
    pause
)

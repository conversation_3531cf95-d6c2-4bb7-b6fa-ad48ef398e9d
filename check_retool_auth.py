#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查 Retool 认证状态
"""

import requests
import json
from datetime import datetime

# 从代码中提取的配置
RETOOL_CONFIG = {
    "domain_name": "edubaa.retool.com",
    "x_xsrf_token": "a7bafe53-5b28-4554-b9d8-8c37895a63d6",
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ4c3JmVG9rZW4iOiJhN2JhZmU1My01YjI4LTQ1NTQtYjlkOC04YzM3ODk1YTYzZDYiLCJ2ZXJzaW9uIjoiMS4yIiwiaWF0IjoxNzUxMjg1MzM3fQ.UDh3aMd9z5n19AtHvvg0bfS9P9QLROBvzc1LFzOtROM"
}

def check_retool_auth():
    """检查 Retool 认证状态"""
    print("🔍 检查 Retool 认证状态")
    print("="*40)
    
    # 检查 token 时间戳
    try:
        import base64
        import json
        
        # 解码 JWT token
        token_parts = RETOOL_CONFIG["accessToken"].split('.')
        if len(token_parts) >= 2:
            # 添加填充
            payload = token_parts[1]
            payload += '=' * (4 - len(payload) % 4)
            
            decoded = base64.b64decode(payload)
            token_data = json.loads(decoded)
            
            print(f"📋 Token 信息:")
            print(f"   XSRF Token: {token_data.get('xsrfToken', 'N/A')}")
            print(f"   版本: {token_data.get('version', 'N/A')}")
            
            if 'iat' in token_data:
                issued_at = datetime.fromtimestamp(token_data['iat'])
                print(f"   签发时间: {issued_at}")
                
                # 检查是否过期（通常 JWT token 有效期较长）
                days_old = (datetime.now() - issued_at).days
                print(f"   已使用天数: {days_old}")
                
                if days_old > 30:
                    print(f"   ⚠️ Token 可能已过期")
                else:
                    print(f"   ✅ Token 时间正常")
    except Exception as e:
        print(f"   ❌ Token 解析失败: {e}")
    
    # 测试 API 访问
    print(f"\n🌐 测试 API 访问:")
    
    # 1. 测试代理列表
    test_agents_api()
    
    # 2. 测试创建线程
    test_create_thread()

def test_agents_api():
    """测试代理 API"""
    url = f"https://{RETOOL_CONFIG['domain_name']}/api/agents"
    
    headers = {
        "x-xsrf-token": RETOOL_CONFIG["x_xsrf_token"],
        "Cookie": f"accessToken={RETOOL_CONFIG['accessToken']}",
        "User-Agent": "Rever-Deno-Port/1.0",
        "Accept": "application/json",
    }
    
    print(f"📡 测试代理列表 API:")
    print(f"   URL: {url}")
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            agents = data.get('agents', [])
            print(f"   ✅ 成功获取 {len(agents)} 个代理")
            
            for agent in agents:
                print(f"      - {agent.get('name', 'Unknown')} ({agent.get('id', 'No ID')})")
                print(f"        模型: {agent.get('data', {}).get('model', 'Unknown')}")
        else:
            print(f"   ❌ 请求失败")
            print(f"   响应: {response.text[:200]}")
            
            if response.status_code == 401:
                print(f"   💡 建议: accessToken 可能已过期")
            elif response.status_code == 403:
                print(f"   💡 建议: XSRF token 可能不匹配")
                
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")

def test_create_thread():
    """测试创建线程"""
    # 首先获取一个代理 ID
    agents_url = f"https://{RETOOL_CONFIG['domain_name']}/api/agents"
    headers = {
        "x-xsrf-token": RETOOL_CONFIG["x_xsrf_token"],
        "Cookie": f"accessToken={RETOOL_CONFIG['accessToken']}",
        "User-Agent": "Rever-Deno-Port/1.0",
        "Accept": "application/json",
    }
    
    try:
        agents_resp = requests.get(agents_url, headers=headers, timeout=10)
        if agents_resp.status_code != 200:
            print(f"🔗 跳过线程测试 (无法获取代理)")
            return
            
        agents = agents_resp.json().get('agents', [])
        if not agents:
            print(f"🔗 跳过线程测试 (没有可用代理)")
            return
            
        agent_id = agents[0]['id']
        print(f"\n🔗 测试创建线程:")
        print(f"   代理 ID: {agent_id}")
        
        thread_url = f"https://{RETOOL_CONFIG['domain_name']}/api/agents/{agent_id}/threads"
        thread_headers = {
            "x-xsrf-token": RETOOL_CONFIG["x_xsrf_token"],
            "Cookie": f"accessToken={RETOOL_CONFIG['accessToken']}",
            "Content-Type": "application/json",
        }
        
        thread_data = {"name": "", "timezone": ""}
        
        thread_resp = requests.post(thread_url, headers=thread_headers, json=thread_data, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if thread_resp.status_code == 200:
            thread_info = thread_resp.json()
            print(f"   ✅ 成功创建线程: {thread_info.get('id', 'Unknown')}")
        else:
            print(f"   ❌ 创建线程失败")
            print(f"   响应: {thread_resp.text[:200]}")
            
    except Exception as e:
        print(f"   ❌ 线程测试异常: {e}")

def main():
    """主函数"""
    print("🔧 Retool 认证检查工具")
    print("="*50)
    
    check_retool_auth()
    
    print(f"\n💡 解决建议:")
    print(f"1. 如果 token 过期，需要重新登录 Retool 获取新的 accessToken")
    print(f"2. 如果 XSRF token 不匹配，需要更新 x_xsrf_token")
    print(f"3. 如果代理无响应，可能是 Retool 服务器问题")
    print(f"4. 检查 Retool 账户是否有足够的配额")

if __name__ == "__main__":
    main()

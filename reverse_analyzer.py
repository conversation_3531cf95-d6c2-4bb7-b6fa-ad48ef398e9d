#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网易云音乐短链逆向分析工具
用于从完整链接推导可能的短链格式
"""

import requests
import time
from urllib.parse import urlparse, parse_qs
import hashlib
import base64

# 导入配置
try:
    from config import *
except ImportError:
    BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    PREFIX = 'G'

def analyze_target_url(url):
    """分析目标URL的特征"""
    print(f"🔍 分析目标URL: {url}")
    
    parsed = urlparse(url)
    query_params = parse_qs(parsed.query)
    
    print(f"📋 URL分析结果:")
    print(f"   域名: {parsed.netloc}")
    print(f"   路径: {parsed.path}")
    print(f"   参数数量: {len(query_params)}")
    
    for key, value in query_params.items():
        print(f"   {key}: {value[0][:50]}{'...' if len(value[0]) > 50 else ''}")
    
    return parsed, query_params

def extract_key_parameters(query_params):
    """提取关键参数用于匹配"""
    key_params = {}
    
    # 提取p参数（通常是主要的加密参数）
    if 'p' in query_params:
        p_value = query_params['p'][0]
        key_params['p'] = p_value
        print(f"🔑 关键参数p: {p_value[:30]}...")
    
    # 提取d参数（通常是设备或时间相关）
    if 'd' in query_params:
        d_value = query_params['d'][0]
        key_params['d'] = d_value
        print(f"🔑 关键参数d: {d_value}")
    
    # 提取userid参数
    if 'userid' in query_params:
        userid = query_params['userid'][0]
        key_params['userid'] = userid
        print(f"🔑 用户ID: {userid}")
    
    return key_params

def generate_search_patterns(key_params):
    """基于关键参数生成搜索模式"""
    patterns = []
    
    # 基于p参数的前几位生成模式
    if 'p' in key_params:
        p_value = key_params['p']
        # 取p参数的前8位作为特征
        p_prefix = p_value[:8]
        patterns.append(f"p={p_prefix}")
    
    # 基于d参数生成模式
    if 'd' in key_params:
        d_value = key_params['d']
        patterns.append(f"d={d_value}")
    
    # 基于userid生成模式
    if 'userid' in key_params:
        userid = key_params['userid']
        patterns.append(f"userid={userid}")
    
    return patterns

def search_in_existing_links(target_url, patterns):
    """在已发现的链接中搜索匹配项"""
    print(f"\n🔍 在已发现的链接中搜索匹配项...")
    
    files_to_search = ['vip_links.txt', 'gift_links.txt']
    matches = []
    
    for filename in files_to_search:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            print(f"📁 搜索文件: {filename}")
            
            for line_num, line in enumerate(lines, 1):
                if line.startswith('#') or not line.strip():
                    continue
                
                # 检查是否包含目标URL的关键部分
                if 'gift-receive' in line and any(pattern in line for pattern in patterns):
                    print(f"✅ 找到匹配项 (行{line_num}): {line.strip()}")
                    matches.append((filename, line_num, line.strip()))
                
                # 检查完整URL匹配
                if target_url in line:
                    print(f"🎯 找到完全匹配 (行{line_num}): {line.strip()}")
                    matches.append((filename, line_num, line.strip()))
        
        except FileNotFoundError:
            print(f"⚠️ 文件 {filename} 不存在")
    
    return matches

def extract_short_link_from_match(match_line):
    """从匹配行中提取短链"""
    # 匹配行格式: 短链接 | 活动类型 | 跳转链接
    parts = match_line.split(' | ')
    if len(parts) >= 1:
        short_link = parts[0].strip()
        if short_link.startswith('http://163cn.tv/'):
            return short_link
    return None

def suggest_scan_range(short_link):
    """基于找到的短链建议扫描范围"""
    if not short_link:
        return None
    
    # 提取短链代码
    code = short_link.split('/')[-1]
    if len(code) >= 6:
        prefix = code[0]
        suffix = code[1:]
        
        print(f"\n📊 短链分析:")
        print(f"   完整代码: {code}")
        print(f"   前缀: {prefix}")
        print(f"   后缀: {suffix}")
        
        return {
            'prefix': prefix,
            'suffix': suffix,
            'full_code': code
        }
    
    return None

def main():
    """主函数"""
    print("🔍 网易云音乐短链逆向分析工具")
    print("="*60)
    
    # 目标URL
    target_url = "https://music.163.com/prime/m/gift-receive?p=PEjjpHWui0zi_2c8XUq00cdOfPv2BRc4IHwWP86PFWDbWDpHm_lsagBuVg7rDKmFM-VXGUlHlQLSyHGU2Ezf3yqbUwWy3wELdIEAQHqT1Yc&d=y-PwQf2u-0kBYizJpd13Yw&redirectFromExternal=true"
    
    # 分析目标URL
    parsed, query_params = analyze_target_url(target_url)
    
    # 提取关键参数
    key_params = extract_key_parameters(query_params)
    
    # 生成搜索模式
    patterns = generate_search_patterns(key_params)
    print(f"\n🔍 生成的搜索模式: {patterns}")
    
    # 在已发现的链接中搜索
    matches = search_in_existing_links(target_url, patterns)
    
    if matches:
        print(f"\n🎉 找到 {len(matches)} 个匹配项:")
        for i, (filename, line_num, line) in enumerate(matches, 1):
            print(f"{i}. {filename}:{line_num}")
            short_link = extract_short_link_from_match(line)
            if short_link:
                print(f"   短链: {short_link}")
                
                # 分析短链格式
                link_info = suggest_scan_range(short_link)
                if link_info:
                    print(f"   建议配置:")
                    print(f"     PREFIX = '{link_info['prefix']}'")
                    print(f"     START_SUFFIX = \"{link_info['suffix']}\"")
            print()
    else:
        print("\n❌ 未找到匹配的短链")
        print("💡 建议:")
        print("1. 继续扫描更多区域")
        print("2. 尝试不同的前缀字母")
        print("3. 检查是否是新的活动链接")

if __name__ == "__main__":
    main()

import requests
import time
import os
from datetime import datetime

# 导入配置文件
try:
    from config import *
    print("✅ 已加载config.py配置文件")
except ImportError:
    print("⚠️ 未找到config.py配置文件，使用默认配置")
    # 默认配置
    PREFIX = 'F'
    START_SUFFIX = "Bm5xqZ"
    SCAN_RANGE = 900000000
    VIP_FILE = 'vip_links.txt'
    GIFT_FILE = 'gift_links.txt'
    LOG_FILE = 'scan_log.txt'
    REQUEST_TIMEOUT = 5
    SLEEP_EVERY = 50
    SLEEP_DURATION = 2
    STATS_EVERY = 1000
    MAGIC_4 = 4
    MAGIC_344 = 344
    BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    ACTIVITY_TYPES = {
        'vip-invite-cashier': '随机88天或188天网易云黑胶VIP',
        'gift-receive': '每月5*7天网易云黑胶VIP（会员等级低可能是5*3天）'
    }

# 兼容性变量（保持向后兼容）
base62_chars = BASE62_CHARS
BASE = 62
prefix = PREFIX
vip_file = VIP_FILE
gift_file = GIFT_FILE
log_file = LOG_FILE

def base62_to_int(s):
    num = 0
    for c in s:
        num = num * BASE + base62_chars.index(c)
    return num

def int_to_base62(n, length=5):
    s = ''
    while n > 0:
        s = base62_chars[n % BASE] + s
        n //= BASE
    return s.rjust(length, '0')

def log_message(message):
    """记录日志信息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write(log_entry + '\n')

def check_short_link(code):
    """检查短链接是否有效并分类保存"""
    url = f"http://163cn.tv/{code}"
    try:
        resp = requests.head(url, allow_redirects=False, timeout=REQUEST_TIMEOUT)
        if resp.status_code in [301, 302] and 'Location' in resp.headers:
            location = resp.headers['Location']

            if 'vip-invite-cashier' in location:
                activity_type = ACTIVITY_TYPES['vip-invite-cashier']
                log_message(f"✅ 发现VIP链接: {url} → {location}")
                log_message(f"   活动类型: {activity_type}")

                with open(vip_file, 'a', encoding='utf-8') as f:
                    f.write(f"{url} | {activity_type} | {location}\n")
                return 'vip'

            elif 'gift-receive' in location:
                activity_type = ACTIVITY_TYPES['gift-receive']
                log_message(f"🎁 发现礼品卡链接: {url} → {location}")
                log_message(f"   活动类型: {activity_type}")

                with open(gift_file, 'a', encoding='utf-8') as f:
                    f.write(f"{url} | {activity_type} | {location}\n")
                return 'gift'

            else:
                print(f"[⚠️ 跳转但不符合目标] {url} → {location}")

        else:
            print(f"[❌ 无效链接] {url} → 状态码: {resp.status_code}")

    except requests.exceptions.Timeout:
        print(f"[⏰ 超时] {url}")
    except Exception as e:
        print(f"[⚠️ 请求错误] {url} → {e}")

    return None

def initialize_files():
    """初始化输出文件，添加文件头信息"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # 初始化VIP链接文件
    if not os.path.exists(vip_file):
        with open(vip_file, 'w', encoding='utf-8') as f:
            f.write(f"# 网易云音乐VIP邀请链接 - 扫描开始时间: {timestamp}\n")
            f.write("# 格式: 短链接 | 活动类型 | 跳转链接\n\n")

    # 初始化礼品卡文件
    if not os.path.exists(gift_file):
        with open(gift_file, 'w', encoding='utf-8') as f:
            f.write(f"# 网易云音乐礼品卡链接 - 扫描开始时间: {timestamp}\n")
            f.write("# 格式: 短链接 | 活动类型 | 跳转链接\n\n")

    # 初始化日志文件
    if not os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"# 网易云音乐短链扫描日志 - 开始时间: {timestamp}\n\n")

def print_statistics(vip_count, gift_count, total_checked, current_id, max_id):
    """打印统计信息"""
    progress = (current_id / max_id) * 100 if max_id > 0 else 0
    print(f"\n{'='*60}")
    print(f"扫描进度: {progress:.2f}% ({current_id:,} / {max_id:,})")
    print(f"已检查链接: {total_checked:,}")
    print(f"发现VIP链接: {vip_count}")
    print(f"发现礼品卡链接: {gift_count}")
    print(f"总计有效链接: {vip_count + gift_count}")
    print(f"{'='*60}")

if __name__ == "__main__":
    # 显示配置信息
    print("🚀 网易云音乐短链扫描器启动")
    print(f"📋 配置信息:")
    print(f"   前缀: {PREFIX}")
    print(f"   起始后缀: {START_SUFFIX}")
    print(f"   扫描范围: {SCAN_RANGE:,}")
    print(f"   请求超时: {REQUEST_TIMEOUT}秒")
    print(f"   休息间隔: 每{SLEEP_EVERY}次检查休息{SLEEP_DURATION}秒")
    print()

    # 配置参数
    start_suffix = START_SUFFIX  # 从配置文件读取
    start_id = base62_to_int(start_suffix)
    max_id = start_id + SCAN_RANGE  # 从配置文件读取

    # 初始化
    initialize_files()
    log_message("开始扫描网易云音乐短链...")
    log_message(f"起始后缀: {start_suffix} (ID: {start_id:,})")
    log_message(f"最大范围: {max_id:,}")
    log_message(f"神奇数字{MAGIC_4}: 顺序遍历{MAGIC_4}个链接")
    log_message(f"神奇数字{MAGIC_344}: 遍历{MAGIC_4}个后跳过{MAGIC_344}个")

    # 统计变量
    i = start_id
    total_checked = 0
    vip_count = 0
    gift_count = 0

    try:
        while i < max_id:
            # 根据神奇数字4，顺序检查4个链接
            for j in range(MAGIC_4):
                current_id = i + j
                suffix = int_to_base62(current_id)
                code = prefix + suffix

                # 显示当前进度
                print(f"检查中: {code} ({current_id:,}/{max_id:,}) - VIP:{vip_count} 礼品:{gift_count}", end='\r')

                result = check_short_link(code)
                total_checked += 1

                if result == 'vip':
                    vip_count += 1
                elif result == 'gift':
                    gift_count += 1

                # 定期休息，避免请求过于频繁
                if total_checked % SLEEP_EVERY == 0:
                    time.sleep(SLEEP_DURATION)

                # 定期显示统计信息
                if total_checked % STATS_EVERY == 0:
                    print_statistics(vip_count, gift_count, total_checked, current_id, max_id)

            # 根据神奇数字344，跳过344个编码
            i += MAGIC_344

    except KeyboardInterrupt:
        log_message("用户中断扫描")
    except Exception as e:
        log_message(f"扫描过程中发生错误: {e}")
    finally:
        # 最终统计
        log_message("扫描结束")
        print_statistics(vip_count, gift_count, total_checked, i, max_id)
        log_message(f"最终统计 - VIP链接: {vip_count}, 礼品卡链接: {gift_count}, 总检查: {total_checked}")

        if vip_count > 0:
            log_message(f"VIP链接已保存到: {vip_file}")
        if gift_count > 0:
            log_message(f"礼品卡链接已保存到: {gift_file}")
        log_message(f"详细日志已保存到: {log_file}")

        print(f"\n🎉 扫描完成！共发现 {vip_count + gift_count} 个有效链接")
        print(f"📁 文件保存位置:")
        print(f"   VIP链接: {vip_file}")
        print(f"   礼品卡链接: {gift_file}")
        print(f"   扫描日志: {log_file}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Retool API 测试脚本
用于诊断部署后的 API 问题
"""

import requests
import json

# 配置
BASE_URL = "http://localhost:8000"  # 修改为您的实际部署地址
API_KEY = "sk-demo-1234567890abcdef"  # 使用代码中的测试密钥

def test_endpoint(method, path, headers=None, data=None, description=""):
    """测试单个端点"""
    url = f"{BASE_URL}{path}"
    print(f"\n🔍 测试: {method} {path}")
    if description:
        print(f"   描述: {description}")
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, timeout=10)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=data, timeout=10)
        else:
            print(f"❌ 不支持的方法: {method}")
            return
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        try:
            response_json = response.json()
            print(f"   响应体: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        except:
            print(f"   响应体 (文本): {response.text[:200]}...")
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: 无法连接到 {url}")
        print(f"   请检查:")
        print(f"   1. 服务是否正在运行")
        print(f"   2. 端口是否正确 (默认8000)")
        print(f"   3. 防火墙设置")
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

def main():
    """主测试函数"""
    print("🚀 Retool API 诊断工具")
    print("="*50)
    
    # 测试基本连接
    print(f"\n📡 测试服务器连接: {BASE_URL}")
    
    # 1. 测试根路径 (应该返回 404)
    test_endpoint("GET", "/", description="根路径 (应该返回404)")
    
    # 2. 测试调试端点 (公开)
    test_endpoint("GET", "/debug", description="调试端点 (公开)")
    
    # 3. 测试模型列表 (公开)
    test_endpoint("GET", "/models", description="模型列表 (公开)")
    
    # 4. 测试模型列表 (需要认证)
    headers_with_auth = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    test_endpoint("GET", "/v1/models", headers=headers_with_auth, description="模型列表 (需要认证)")
    
    # 5. 测试聊天完成 (需要认证)
    chat_data = {
        "model": "claude-sonnet-4",  # 假设的模型名
        "messages": [
            {"role": "user", "content": "Hello, how are you?"}
        ],
        "stream": False
    }
    test_endpoint("POST", "/v1/chat/completions", headers=headers_with_auth, data=chat_data, description="聊天完成 (需要认证)")
    
    # 6. 测试不存在的端点
    test_endpoint("GET", "/nonexistent", description="不存在的端点 (应该返回404)")
    
    print(f"\n📊 诊断总结:")
    print(f"1. 如果所有请求都显示 '连接失败'，说明服务未启动或端口错误")
    print(f"2. 如果 /models 返回空列表，说明 Retool 账户配置有问题")
    print(f"3. 如果认证端点返回 401/403，说明 API 密钥配置有问题")
    print(f"4. 如果聊天完成失败，说明 Retool 代理配置有问题")

if __name__ == "__main__":
    main()

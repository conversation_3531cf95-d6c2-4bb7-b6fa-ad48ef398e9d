#!/usr/bin/env python3
import requests

# 快速测试脚本
BASE_URL = "https://mrever.deno.dev"  # 您的 Deno 部署地址

print("🔍 快速诊断 Retool API")
print("="*30)

# 测试可用的端点
endpoints = [
    ("GET", "/"),
    ("GET", "/debug"),
    ("GET", "/models"),
    ("GET", "/v1/models"),
]

for method, path in endpoints:
    try:
        url = f"{BASE_URL}{path}"
        if method == "GET":
            resp = requests.get(url, timeout=5)
        print(f"{method} {path} → {resp.status_code}")
        if resp.status_code != 200:
            try:
                print(f"  错误: {resp.json()}")
            except:
                print(f"  响应: {resp.text[:100]}")
    except Exception as e:
        print(f"{method} {path} → 连接失败: {e}")

print("\n💡 常见问题:")
print("1. 连接失败 → 检查服务是否运行在正确端口")
print("2. 404错误 → 检查访问的路径是否正确")
print("3. 401/403 → 检查API密钥配置")

# 网易云音乐短链扫描器配置文件

# ==================== 基础配置 ====================

# 短链前缀（第一位字符）
PREFIX = 'G'

# 起始短链后6位（从论坛获取的最新有效链接）
# 示例: 如果看到链接 http://163cn.tv/GAB2vRr，则填写 "AB2vRr"
START_SUFFIX = "AB2vRr"

# 扫描范围（从起始位置开始扫描多少个编码）
SCAN_RANGE = 900000000

# ==================== 文件配置 ====================

# 输出文件路径
VIP_FILE = 'vip_links.txt'          # VIP邀请链接保存文件
GIFT_FILE = 'gift_links.txt'        # 礼品卡链接保存文件
LOG_FILE = 'scan_log.txt'           # 扫描日志文件

# ==================== 扫描配置 ====================

# 请求设置
REQUEST_TIMEOUT = 5                 # 请求超时时间（秒）
SLEEP_EVERY = 50                    # 每检查多少个链接休息一次
SLEEP_DURATION = 2                  # 休息时间（秒）

# 显示设置
STATS_EVERY = 1000                  # 每多少次检查显示一次详细统计

# ==================== 高级配置 ====================

# 网易云编码规律的神奇数字
MAGIC_4 = 4                         # 顺序遍历的链接数
MAGIC_344 = 344                     # 跳跃的编码数（86*4）

# Base62字符表（请勿修改，除非了解编码规律）
BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

# ==================== 活动类型说明 ====================

ACTIVITY_TYPES = {
    'vip-invite-cashier': '随机88天或188天网易云黑胶VIP',
    'gift-receive': '每月5*7天网易云黑胶VIP（会员等级低可能是5*3天）'
}

# ==================== 使用说明 ====================

"""
配置说明：

1. PREFIX: 短链的第一位字符，通常为 'F'
2. START_SUFFIX: 从论坛获取的最新有效短链的后6位
3. SCAN_RANGE: 扫描范围，数值越大扫描越久但可能找到更多链接

扫描策略：
- 根据网易云的编码规律，每次检查4个连续短链
- 然后跳过344个编码继续下一轮
- 这样可以高效覆盖所有可能的有效链接

性能调优：
- 减小 SLEEP_DURATION 可以提高扫描速度，但可能被限制访问
- 增大 SLEEP_EVERY 可以降低服务器压力
- 调整 REQUEST_TIMEOUT 平衡速度和稳定性
"""

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网易云音乐短链查找工具
通过暴力搜索找到对应的短链
"""

import requests
import time
import random
from urllib.parse import urlparse, parse_qs

# 导入配置
try:
    from config import *
except ImportError:
    BASE62_CHARS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    PREFIX = 'G'
    REQUEST_TIMEOUT = 5

def normalize_url(url):
    """标准化URL，移除不重要的参数"""
    parsed = urlparse(url)
    query_params = parse_qs(parsed.query)
    
    # 保留关键参数
    key_params = {}
    for key in ['p', 'd', 'userid']:
        if key in query_params:
            key_params[key] = query_params[key][0]
    
    return key_params

def check_short_link_match(short_code, target_params):
    """检查短链是否匹配目标参数"""
    url = f"http://163cn.tv/{short_code}"
    try:
        resp = requests.head(url, allow_redirects=False, timeout=REQUEST_TIMEOUT)
        if resp.status_code in [301, 302] and 'Location' in resp.headers:
            location = resp.headers['Location']
            
            if 'gift-receive' in location:
                # 解析跳转URL的参数
                location_params = normalize_url(location)
                
                # 检查关键参数是否匹配
                matches = 0
                total_params = len(target_params)
                
                for key, value in target_params.items():
                    if key in location_params and location_params[key] == value:
                        matches += 1
                
                match_rate = matches / total_params if total_params > 0 else 0
                
                if match_rate > 0.5:  # 超过50%的参数匹配
                    print(f"🎯 找到匹配短链: {url}")
                    print(f"   匹配度: {match_rate:.2%} ({matches}/{total_params})")
                    print(f"   跳转到: {location}")
                    return True, location
                elif matches > 0:
                    print(f"🔍 部分匹配: {url} (匹配度: {match_rate:.2%})")
        
    except Exception as e:
        pass
    
    return False, None

def base62_to_int(s):
    """Base62字符串转整数"""
    num = 0
    for c in s:
        num = num * 62 + BASE62_CHARS.index(c)
    return num

def int_to_base62(n, length=6):
    """整数转Base62字符串"""
    s = ''
    while n > 0:
        s = BASE62_CHARS[n % 62] + s
        n //= 62
    return s.rjust(length, BASE62_CHARS[0])

def smart_search_around_known_links(target_params):
    """在已知链接附近智能搜索"""
    print("🔍 在已知链接附近搜索...")
    
    # 读取已知的礼品卡链接
    known_codes = []
    try:
        with open('gift_links.txt', 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('http://163cn.tv/'):
                    code = line.split('/')[-1].split(' ')[0]
                    if len(code) >= 6:
                        known_codes.append(code)
    except FileNotFoundError:
        print("⚠️ 未找到gift_links.txt文件")
        return False, None
    
    if not known_codes:
        print("⚠️ 没有已知的礼品卡链接")
        return False, None
    
    print(f"📋 找到 {len(known_codes)} 个已知礼品卡链接")
    
    # 在每个已知链接附近搜索
    for known_code in known_codes[-3:]:  # 只检查最近的3个
        print(f"🔍 在 {known_code} 附近搜索...")
        
        if len(known_code) >= 6:
            prefix = known_code[0]
            suffix = known_code[1:]
            base_id = base62_to_int(suffix)
            
            # 在附近范围搜索
            search_range = 1000  # 搜索范围
            for offset in range(-search_range, search_range, 10):
                new_id = base_id + offset
                if new_id < 0:
                    continue
                
                new_suffix = int_to_base62(new_id, len(suffix))
                new_code = prefix + new_suffix
                
                print(f"检查: {new_code}", end='\r')
                
                found, location = check_short_link_match(new_code, target_params)
                if found:
                    return True, new_code
                
                # 避免请求过于频繁
                if offset % 50 == 0:
                    time.sleep(1)
    
    return False, None

def random_search(target_params, max_attempts=500):
    """随机搜索短链"""
    print(f"🎲 开始随机搜索 (最多{max_attempts}次尝试)...")
    
    prefixes = ['G', 'F', 'H', 'E', 'D']  # 常见的前缀
    
    for attempt in range(max_attempts):
        # 随机生成短链
        prefix = random.choice(prefixes)
        suffix = ''.join(random.choices(BASE62_CHARS, k=6))
        code = prefix + suffix
        
        print(f"尝试 {attempt+1}/{max_attempts}: {code}", end='\r')
        
        found, location = check_short_link_match(code, target_params)
        if found:
            return True, code
        
        # 每50次尝试休息一下
        if attempt % 50 == 0 and attempt > 0:
            time.sleep(2)
    
    return False, None

def main():
    """主函数"""
    print("🔍 网易云音乐短链查找工具")
    print("="*60)
    
    # 目标URL
    target_url = "https://music.163.com/prime/m/gift-receive?p=PEjjpHWui0zi_2c8XUq00cdOfPv2BRc4IHwWP86PFWDbWDpHm_lsagBuVg7rDKmFM-VXGUlHlQLSyHGU2Ezf3yqbUwWy3wELdIEAQHqT1Yc&d=y-PwQf2u-0kBYizJpd13Yw&redirectFromExternal=true"
    
    print(f"🎯 目标URL: {target_url[:80]}...")
    
    # 提取目标参数
    target_params = normalize_url(target_url)
    print(f"🔑 关键参数: {target_params}")
    
    # 方法1: 在已知链接附近搜索
    found, result = smart_search_around_known_links(target_params)
    if found:
        print(f"\n🎉 成功找到对应短链: http://163cn.tv/{result}")
        
        # 提取配置信息
        if len(result) >= 6:
            prefix = result[0]
            suffix = result[1:]
            print(f"\n📋 配置信息:")
            print(f"   PREFIX = '{prefix}'")
            print(f"   START_SUFFIX = \"{suffix}\"")
        return
    
    print("\n❌ 在已知链接附近未找到匹配项")
    
    # 方法2: 随机搜索
    if input("是否进行随机搜索? (y/n): ").lower() == 'y':
        found, result = random_search(target_params)
        if found:
            print(f"\n🎉 随机搜索成功找到: http://163cn.tv/{result}")
            
            # 提取配置信息
            if len(result) >= 6:
                prefix = result[0]
                suffix = result[1:]
                print(f"\n📋 配置信息:")
                print(f"   PREFIX = '{prefix}'")
                print(f"   START_SUFFIX = \"{suffix}\"")
        else:
            print("\n❌ 随机搜索未找到匹配项")
    
    print("\n💡 建议:")
    print("1. 这可能是一个新的活动链接")
    print("2. 尝试扩大搜索范围")
    print("3. 检查是否有其他前缀字母")

if __name__ == "__main__":
    main()

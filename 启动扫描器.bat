@echo off
chcp 65001 >nul
title 网易云音乐短链扫描器

echo.
echo ========================================
echo    网易云音乐短链扫描器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未检测到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检测正常

REM 检查是否存在启动脚本
if exist "start_scan.py" (
    echo ✅ 找到启动脚本
    python start_scan.py
) else if exist "short_link_checker.py" (
    echo ✅ 找到主程序
    python short_link_checker.py
) else (
    echo ❌ 未找到程序文件
    echo 请确保以下文件存在:
    echo   - short_link_checker.py
    echo   - start_scan.py (可选)
)

echo.
echo 按任意键退出...
pause >nul

# 网易云音乐礼品卡、VIP邀请链接分析器 🎵

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyQt6](https://img.shields.io/badge/PyQt6-GUI-green.svg)](https://pypi.org/project/PyQt6/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/Platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)]()

> **作者**: suimi
> **版本**: 2.0
> **技术栈**: PyQt6 + 网易云音乐API + 多线程并发
> **仅供学习研究使用，请勿用于商业用途**

## 📖 项目简介

网易云音乐礼品卡、VIP邀请链接分析器是一个功能强大的工具，专门用于批量分析网易云音乐礼品卡和VIP邀请链接的状态。通过直接调用网易云音乐API接口，实现高效、准确的链接状态检测。

### 🌟 核心特性

- **🎁 礼品卡分析**: 批量检测礼品卡状态（可领取/已过期/已领取完）
- **👑 VIP链接检测**: 智能识别VIP邀请链接并检查有效期
- **🚀 高性能处理**: 多线程并发处理，支持暂停/继续/停止
- **📊 详细统计**: 实时显示分析进度和详细统计信息
- **💾 智能保存**: 自动分类保存不同状态的链接
- **🔄 实时更新**: 实时显示分析结果，支持结果过滤
- **🛡️ 异常处理**: 完善的API异常检测和分类

## 📁 项目结构

```
wyy/
├── gift_analyzer_ui.py      # PyQt6 图形界面主程序
├── optimal_gift_analyzer.py # 核心分析引擎
├── requirements.txt         # 项目依赖
└── README.MD               # 项目文档
```

## 🔧 安装与配置

### 环境要求

- Python 3.8+
- Windows/Linux/macOS

### 安装依赖

```bash
# 进入项目目录
cd wyy

# 安装依赖包
pip install -r requirements.txt
```

### 依赖说明

- **PyQt6**: 图形界面框架
- **requests**: HTTP请求库
- **pycryptodome**: 网易云音乐API加密算法

## 🚀 使用方法

### 启动程序

```bash
python gift_analyzer_ui.py
```

### 基本操作流程

1. **加载链接**
   - 点击"📁 加载链接文件"按钮选择包含链接的txt文件
   - 或直接在输入框中粘贴链接（每行一个）

2. **配置分析参数**
   - 设置线程数（建议5-20，根据网络情况调整）
   - 选择要显示的结果类型

3. **开始分析**
   - 点击"🚀 开始分析"按钮
   - 实时查看分析进度和结果
   - 支持暂停/继续/停止操作

4. **查看结果**
   - **礼品卡列表**: 显示礼品卡详细信息
   - **VIP邀请列表**: 显示VIP链接有效期信息

5. **保存结果**
   - 点击"💾 保存结果"导出分析结果
   - 自动分类保存不同状态的链接

### 支持的链接格式

- **礼品卡链接**: `http://163cn.tv/xxxxxx`
- **VIP邀请链接**: 重定向到 `vip-invite-cashier` 的链接

## 📊 功能详解

### 礼品卡分析功能

- **状态检测**: 
  - ✅ 可领取：显示剩余数量
  - ⏰ 已过期：显示过期时间
  - 🎯 已领取完：显示总数量
  - ❌ 链接无效：显示错误原因

- **详细信息**:
  - 发送者昵称
  - 礼品类型和价值
  - 过期时间（北京时间）
  - 领取进度

### VIP链接分析功能

- **有效期检测**:
  - 🔍 API方法：直接调用官方API获取准确信息
  - 📄 页面解析：备用方法，解析页面内容
  - ⏰ 时间转换：自动转换为北京时间

- **详细信息**:
  - 邀请者信息
  - VIP天数
  - 剩余有效期
  - 检测方法标识

### 高级功能

- **暂停/继续**: 支持分析过程中的暂停和继续
- **失效链接删除**: 一键删除检测到的失效链接
- **结果过滤**: 按状态类型过滤显示结果
- **批量导出**: 支持导出特定状态的链接
- **实时统计**: 显示各种状态的数量和比例

## 🔧 核心技术

### 网易云音乐API加密

项目实现了完整的网易云音乐API加密算法：

- **AES加密**: 双重AES-CBC加密
- **RSA加密**: 对随机密钥进行RSA加密
- **参数构造**: 自动构造符合API要求的请求参数

### 多线程并发处理

- **ThreadPoolExecutor**: 使用线程池管理并发任务
- **线程安全**: 使用锁机制保护共享资源
- **实时反馈**: 单个结果完成即时显示

### 异常处理机制

- **网络异常**: ConnectionError, Timeout, HTTPError等
- **API异常**: 业务错误码、服务器错误等
- **系统异常**: 未知错误的兜底处理

## 📈 性能优化

- **智能线程数**: 根据系统配置建议最优线程数
- **批量处理**: 支持大量链接的高效处理
- **内存优化**: 实时释放不需要的数据
- **网络优化**: 复用HTTP连接，减少握手开销

## 🛠️ 开发说明

### 代码架构

- **UI层**: `gift_analyzer_ui.py` - PyQt6界面和用户交互
- **业务层**: `optimal_gift_analyzer.py` - 核心分析逻辑
- **工具层**: 加密算法、时间转换等工具函数

### 扩展开发

如需添加新功能，建议：

1. 在 `optimal_gift_analyzer.py` 中添加核心逻辑
2. 在 `gift_analyzer_ui.py` 中添加UI交互
3. 保持线程安全和异常处理的一致性

## ⚠️ 注意事项

1. **使用频率**: 请合理控制请求频率，避免对服务器造成压力
2. **网络环境**: 建议在稳定的网络环境下使用
3. **数据备份**: 重要数据请及时备份
4. **合规使用**: 请遵守相关服务条款，仅用于个人学习研究

## 🐛 常见问题

### Q: 分析速度很慢怎么办？
A: 可以适当增加线程数，但不建议超过20个，以免触发API限制。

### Q: 出现大量API异常怎么办？
A: 检查网络连接，降低线程数，或稍后重试。

### Q: VIP链接检测不准确怎么办？
A: 程序会自动尝试API和页面解析两种方法，通常能保证准确性。

### Q: 如何批量处理大量链接？
A: 建议分批处理，每批不超过10000个链接，避免内存占用过高。

## 📝 使用示例

### 命令行使用（仅分析引擎）

```python
from optimal_gift_analyzer import OptimalGiftAnalyzer

# 创建分析器实例
analyzer = OptimalGiftAnalyzer()

# 分析单个链接
result = analyzer.analyze_gift_link("http://163cn.tv/GBm6AHn")
print(result)

# 批量分析
links = ["http://163cn.tv/link1", "http://163cn.tv/link2"]
results = analyzer.batch_analyze(links, max_workers=5)

# 保存结果
analyzer.save_results(results, "analysis_results.json")

# 打印统计信息
analyzer.print_statistics(results)

# 过滤并保存不同状态的链接
analyzer.filter_and_save(results)
```

### 图形界面使用

1. **启动程序**:
   ```bash
   python gift_analyzer_ui.py
   ```

2. **加载测试数据**:
   - 创建 `test_links.txt` 文件
   - 每行一个链接
   - 通过界面加载文件

3. **配置分析参数**:
   - 线程数: 5-20（根据网络情况）
   - 过滤选项: 选择要显示的结果类型

4. **查看分析结果**:
   - 实时查看进度条和状态信息
   - 在结果表格中查看详细信息
   - 使用过滤功能筛选特定状态

## 🔍 API响应示例

### 成功的礼品卡响应

```json
{
  "status": "success",
  "gift_status": "available",
  "status_text": "可领取 (2/3)",
  "sender_name": "用户昵称",
  "gift_type": "黑胶VIP月卡",
  "gift_price": 15,
  "total_count": 3,
  "used_count": 1,
  "available_count": 2,
  "expire_date": "2024-12-31 23:59:59",
  "is_expired": false
}
```

### VIP链接响应

```json
{
  "status": "success",
  "is_vip_link": true,
  "vip_status": "valid",
  "vip_status_text": "VIP有效 (剩余: 25.3天)",
  "gift_type": "VIP邀请 (30天)",
  "sender": "邀请者昵称",
  "expire_date": "2024-12-31 23:59:59 (北京时间)",
  "vip_expiry_check": {
    "is_valid": true,
    "method": "api",
    "remaining_days": 25.3
  }
}
```

### 异常响应

```json
{
  "status": "api_exception",
  "error_type": "api_exception",
  "error_category": "timeout",
  "error_message": "请求超时",
  "technical_details": "ReadTimeout: HTTPSConnectionPool..."
}
```

## 🔧 配置说明

### 线程数配置建议

| 网络环境 | 建议线程数 | 说明 |
|---------|-----------|------|
| 家庭宽带 | 5-10 | 稳定性优先 |
| 企业网络 | 10-15 | 平衡性能和稳定性 |
| 服务器环境 | 15-20 | 性能优先 |

### 文件输出说明

程序会自动生成以下文件：

- `可领取礼品卡.txt`: 可以领取的礼品卡链接
- `已过期礼品卡.txt`: 已过期的礼品卡链接
- `已领取礼品卡.txt`: 已被领取完的礼品卡链接
- `vip_links.txt`: 有效的VIP邀请链接
- `gift_links.txt`: 礼品卡链接（如果检测到）
- `gift_analysis_results.json`: 完整的分析结果

## 🚨 错误处理

### 常见错误类型

1. **网络错误**:
   - `connection_error`: 网络连接失败
   - `timeout`: 请求超时
   - `ssl_error`: SSL证书错误

2. **API错误**:
   - `forbidden`: API访问被拒绝(403)
   - `rate_limit`: 请求频率过高(429)
   - `server_error`: 服务器错误(5xx)

3. **业务错误**:
   - `not_found`: 链接不存在(404)
   - `invalid`: 不是礼品卡链接
   - `expired`: 链接已过期

### 错误恢复策略

- **自动重试**: 对于临时性网络错误
- **降级处理**: API失败时使用页面解析
- **异常分类**: 详细记录错误类型和原因
- **用户提示**: 友好的错误信息显示

## 📊 性能监控

### 系统资源监控

程序内置性能监控功能：

- **线程数量**: 实时显示活跃线程数
- **CPU使用率**: 监控CPU占用情况
- **内存使用**: 监控内存占用情况
- **网络状态**: 监控请求成功率

### 性能优化建议

1. **合理设置线程数**: 避免过多线程导致系统负载过高
2. **分批处理**: 大量链接分批处理，避免内存溢出
3. **网络优化**: 使用稳定的网络环境
4. **定期清理**: 及时清理不需要的结果数据

## ⚖️ 免责声明

### � 使用条款

本软件（"网易云音乐礼品卡分析器"）仅供**学习、研究和个人使用**。使用本软件即表示您同意以下条款：

### 📋 责任限制

1. **非商业用途**: 本软件严禁用于任何商业用途、盈利活动或违法行为
2. **技术研究**: 本项目仅用于技术学习和API接口研究，不得用于恶意攻击或滥用
3. **服务条款**: 用户必须遵守网易云音乐的服务条款和使用协议
4. **频率限制**: 用户应合理控制请求频率，避免对服务器造成不必要的负担

### 🚫 禁止行为

- ❌ 大规模批量请求导致服务器压力
- ❌ 用于商业目的或盈利活动
- ❌ 破解、逆向工程或恶意攻击
- ❌ 侵犯他人隐私或合法权益
- ❌ 违反相关法律法规的行为


### 📞 联系与反馈

- **问题反馈**: 通过GitHub Issues提交技术问题
- **建议改进**: 欢迎提出合理的功能建议
- **安全漏洞**: 请负责任地披露安全问题

### ⚠️ 特别提醒

- 本软件可能因网易云音乐API变更而失效
- 请定期检查更新，确保使用最新版本
- 如发现任何违法违规使用，请立即停止并联系开发者

## �📄 许可证

本项目采用 **MIT License** 开源协议，仅供学习和研究使用，请勿用于商业用途，否则后果自负。

**使用本软件即表示您已阅读、理解并同意上述所有条款。**

---

**开发者**: suimi
**最后更新**: 2025年
**联系方式**: 通过GitHub Issues反馈问题

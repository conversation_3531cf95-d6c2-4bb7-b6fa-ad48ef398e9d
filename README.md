# 网易云音乐短链扫描器

## 📖 项目简介

这是一个用于扫描网易云音乐短链的Python脚本，可以自动发现并分类保存不同类型的活动链接。

## 🎯 支持的活动类型

1. **VIP邀请链接** (`vip-invite-cashier`)
   - 随机88天或188天的网易云黑胶VIP
   - 数量较多，是主要目标

2. **礼品卡链接** (`gift-receive`)
   - 每月5×7天的网易云黑胶VIP
   - 会员等级低可能是5×3天
   - 数量相对较少

## 🔧 技术原理

### Base62编码规律
网易云的短链使用Base62编码，字符顺序为：
```
abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789
```

### 神奇数字
- **数字4**: 顺序的四个短链中有三个是404页面，需要顺序遍历4次才能找到一个有效链接
- **数字344**: 网易云顺序的86×4=344个短链中，有效的86个短链指向同一个跳转链接

### 扫描策略
1. 顺序检查4个连续的短链
2. 跳过344个编码继续下一轮检查
3. 这样可以高效地覆盖所有可能的有效链接

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install requests
```

### 2. 配置参数
编辑 `short_link_checker.py` 文件中的配置：

```python
# 修改起始短链后6位（从论坛获取最新有效链接）
start_suffix = "Bm5xqZ"

# 修改第一位短链前缀
prefix = 'F'

# 设置扫描范围
max_id = start_id + 900000000
```

### 3. 运行脚本
```bash
python short_link_checker.py
```

## 📁 输出文件

运行后会生成以下文件：

1. **vip_links.txt** - VIP邀请链接
2. **gift_links.txt** - 礼品卡链接  
3. **scan_log.txt** - 详细扫描日志

每个链接文件格式：
```
短链接 | 活动类型 | 跳转链接
```

## 📊 实时监控

脚本运行时会显示：
- 当前检查的短链
- 实时统计（VIP链接数、礼品卡链接数）
- 扫描进度
- 每1000次检查显示详细统计

## ⚙️ 高级配置

```python
# 扫描频率控制
sleep_every = 50        # 每检查50个链接休息一次
sleep_duration = 2      # 休息时间（秒）
stats_every = 1000      # 每1000次检查显示统计

# 请求超时设置
timeout = 5             # 请求超时时间（秒）
```

## 🛡️ 注意事项

1. **合理使用**: 请合理控制扫描频率，避免对服务器造成过大压力
2. **网络稳定**: 确保网络连接稳定，避免因网络问题导致误判
3. **起始位置**: 建议从论坛最新分享的有效链接开始扫描
4. **中断恢复**: 支持Ctrl+C中断，会保存已扫描的结果

## 📈 性能优化建议

1. **并行化**: 可以考虑使用多线程或异步请求提高扫描速度
2. **智能跳跃**: 根据404响应模式动态调整跳跃策略
3. **缓存机制**: 对已检查的链接进行缓存，避免重复检查
4. **代理轮换**: 使用代理池避免IP限制

## 🎉 成功案例

根据作者经验，使用此脚本几小时内获得了14万天VIP，证明了算法的有效性。

## ⚠️ 免责声明

本工具仅供学习和研究使用，请遵守相关服务条款和法律法规。
